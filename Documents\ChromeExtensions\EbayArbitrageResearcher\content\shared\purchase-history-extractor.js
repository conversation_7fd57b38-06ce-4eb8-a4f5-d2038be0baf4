/**
 * 🎯 AUTHENTICATED: Purchase History Extractor for Background Tabs
 * This script is injected into eBay purchase history pages to extract real transaction data
 */

/**
 * Extract purchase history from the current page using exact user-provided selectors
 */
function extractPurchaseHistoryFromPage(itemId) {
  console.log(`🎯 AUTHENTICATED DEBUG: Starting extraction for item ${itemId} from current page`);
  console.log(`🔧 AUTHENTICATED DEBUG: Current URL: ${window.location.href}`);
  console.log(`🔧 AUTHENTICATED DEBUG: Page title: ${document.title}`);

  try {
    // Check if we're on a login page
    if (document.title.includes('Sign in') || document.body.textContent.includes('Sign in to your account')) {
      console.log(`❌ AUTHENTICATED DEBUG: Redirected to login page for item ${itemId}`);
      console.log(`🔧 AUTHENTICATED DEBUG: Page content preview:`, document.body.textContent.substring(0, 200));
      sendExtractionResult(itemId, null);
      return;
    }

    // Check for error pages
    if (document.title.includes('Error') || document.body.textContent.includes('not found')) {
      console.log(`❌ AUTHENTICATED DEBUG: Error page detected for item ${itemId}`);
      console.log(`🔧 AUTHENTICATED DEBUG: Page content preview:`, document.body.textContent.substring(0, 200));
      sendExtractionResult(itemId, null);
      return;
    }

    console.log(`🔧 AUTHENTICATED DEBUG: Page appears valid, looking for table...`);

    // 🔍 DEBUG: Let's see what's actually on the page
    console.log(`🔧 AUTHENTICATED DEBUG: Page HTML length: ${document.documentElement.outerHTML.length}`);
    console.log(`🔧 AUTHENTICATED DEBUG: All tables on page:`, document.querySelectorAll('table').length);
    console.log(`🔧 AUTHENTICATED DEBUG: All divs on page:`, document.querySelectorAll('div').length);
    console.log(`🔧 AUTHENTICATED DEBUG: Page title: "${document.title}"`);
    console.log(`🔧 AUTHENTICATED DEBUG: Page URL: ${window.location.href}`);

    // Check for specific eBay content
    const bodyText = document.body.textContent;
    console.log(`🔧 DEBUG: Page contains "purchase history": ${bodyText.toLowerCase().includes('purchase history')}`);
    console.log(`🔧 DEBUG: Page contains "sign in": ${bodyText.toLowerCase().includes('sign in')}`);
    console.log(`🔧 DEBUG: Page contains "user id": ${bodyText.toLowerCase().includes('user id')}`);
    console.log(`🔧 DEBUG: Page contains "buy it now price": ${bodyText.toLowerCase().includes('buy it now price')}`);
    console.log(`🔧 DEBUG: Page contains "quantity": ${bodyText.toLowerCase().includes('quantity')}`);
    console.log(`🔧 DEBUG: Page contains "date of purchase": ${bodyText.toLowerCase().includes('date of purchase')}`);

    // Show more of the page content to see what we're actually getting
    console.log(`🔧 DEBUG: Full page text (first 1000 chars):`, bodyText.substring(0, 1000));

    // Check for any tables at all
    const allTables = document.querySelectorAll('table');
    console.log(`🔧 DEBUG: Found ${allTables.length} tables on page`);
    allTables.forEach((table, i) => {
      console.log(`🔧 DEBUG: Table ${i} preview:`, table.outerHTML.substring(0, 200));
    });

    // Look for specific eBay purchase history indicators
    const hasUserID = document.body.textContent.includes('User ID');
    const hasBuyItNow = document.body.textContent.includes('Buy it now price');
    const hasQuantity = document.body.textContent.includes('Quantity');
    const hasDateOfPurchase = document.body.textContent.includes('Date of purchase');

    console.log(`🔍 AUTHENTICATED DEBUG: Purchase history indicators:`, {
      hasUserID,
      hasBuyItNow,
      hasQuantity,
      hasDateOfPurchase
    });

    // 🎯 USER'S EXACT SELECTOR: Find the table
    const table = document.querySelector('table');
    if (!table) {
      console.log(`❌ AUTHENTICATED DEBUG: No table found for item ${itemId}`);
      console.log(`🔧 AUTHENTICATED DEBUG: Available elements:`, document.querySelectorAll('*').length);

      // Try alternative selectors
      const allTables = document.querySelectorAll('table, [role="table"], .table, [class*="table"]');
      console.log(`🔍 AUTHENTICATED DEBUG: Alternative table selectors found:`, allTables.length);

      allTables.forEach((t, i) => {
        console.log(`🔍 Table ${i}:`, t.outerHTML.substring(0, 200));
      });

      sendExtractionResult(itemId, null);
      return;
    }

    console.log(`✅ AUTHENTICATED DEBUG: Found table for item ${itemId}`);
    console.log(`📋 AUTHENTICATED DEBUG: Table HTML preview:`, table.outerHTML.substring(0, 500));

    // 🎯 USER'S EXACT SELECTOR: Get all rows and skip header (index 0)
    const rows = table.querySelectorAll('tr');
    console.log(`📋 AUTHENTICATED: Found ${rows.length} total rows (including header)`);

    const transactions = [];

    // Skip header row (index 0), process data rows (index 1+)
    for (let i = 1; i < rows.length; i++) {
      const row = rows[i];
      
      // 🎯 USER'S EXACT SELECTOR: Get all cells in this row
      const cells = row.querySelectorAll('td');
      
      console.log(`📋 Row ${i}: ${cells.length} cells`);
      
      // Log cell contents for debugging
      if (cells.length > 0) {
        const cellTexts = Array.from(cells).map(cell => cell.innerText.trim());
        console.log(`📋 Row ${i} cells:`, cellTexts);
      }

      // 🎯 USER'S EXACT STRUCTURE: Expect exactly 4 columns [userId, price, quantity, date]
      if (cells.length === 4) {
        const transaction = {
          userId: cells[0].innerText.trim(),
          price: cells[1].innerText.trim(),
          quantity: cells[2].innerText.trim(),
          date: cells[3].innerText.trim()
        };

        // Validate we have meaningful data
        if (transaction.userId && transaction.date) {
          transactions.push(transaction);
          console.log(`🎯 AUTHENTICATED SUCCESS: Extracted transaction:`, transaction);
        } else {
          console.log(`⚠️ Row ${i} missing required data:`, transaction);
        }
      } else {
        console.log(`⚠️ Row ${i} has ${cells.length} cells, expected 4`);
      }
    }

    if (transactions.length === 0) {
      console.log(`❌ AUTHENTICATED: No valid transactions found for item ${itemId}`);
      sendExtractionResult(itemId, null);
      return;
    }

    console.log(`🎯 AUTHENTICATED SUCCESS: Found ${transactions.length} transactions with REAL DATES for item ${itemId}`);
    
    // Log all extracted transactions
    transactions.forEach((t, index) => {
      console.log(`Transaction ${index + 1}: User ${t.userId}, ${t.quantity}x @ ${t.price} on ${t.date}`);
    });

    // Convert to sales data format
    const salesData = {
      salesFrequency: transactions.length,
      salesHistory: transactions.map(t => ({
        userId: t.userId,
        price: parsePrice(t.price),
        quantity: parseInt(t.quantity) || 1,
        date: t.date, // Keep original eBay date format: "20 Jul 2025 at 2:06:55pm PDT"
        dateText: t.date,
        originalDate: t.date,
        formattedDate: t.date, // Use original date directly
        priceText: t.price,
        source: 'authenticated-purchase-history'
      })),
      lastSoldDate: transactions[0]?.date || null,
      averageSalePrice: calculateAveragePrice(transactions),
      salesVelocity: transactions.length, // Will be calculated properly later
      demandScore: Math.min(transactions.length * 2, 10), // Simple demand scoring
      extractedFrom: 'authenticated-purchase-history',
      verificationNote: 'Extracted from authenticated eBay purchase history page',
      timestamp: Date.now()
    };

    console.log(`🎯 AUTHENTICATED: Final sales data for ${itemId}:`, salesData);
    sendExtractionResult(itemId, salesData);

  } catch (error) {
    console.error(`❌ AUTHENTICATED: Error extracting purchase history for ${itemId}:`, error);
    sendExtractionResult(itemId, null);
  }
}

/**
 * Parse price from eBay format (e.g., "US $115.00" -> 115.00)
 */
function parsePrice(priceText) {
  if (!priceText) return null;
  const match = priceText.match(/\$?(\d+\.?\d*)/);
  return match ? parseFloat(match[1]) : null;
}

/**
 * Calculate average price from transactions
 */
function calculateAveragePrice(transactions) {
  if (transactions.length === 0) return null;
  
  const prices = transactions.map(t => parsePrice(t.price)).filter(p => p !== null);
  if (prices.length === 0) return null;
  
  return prices.reduce((sum, price) => sum + price, 0) / prices.length;
}

/**
 * Send extraction results back to content script with immediate UI update
 */
function sendExtractionResult(itemId, salesData) {
  console.log(`📡 SINGLE ITEM: Sending extraction result for ${itemId} at ${new Date().toLocaleTimeString()}`);
  console.log(`📊 SINGLE ITEM: Sales data being sent:`, salesData);

  chrome.runtime.sendMessage({
    action: 'purchaseHistoryExtracted',
    itemId: itemId,
    data: salesData,
    timestamp: Date.now()
  }, () => {
    if (chrome.runtime.lastError) {
      console.log(`⚠️ SINGLE ITEM: Could not send extraction result: ${chrome.runtime.lastError.message}`);
    } else {
      console.log(`✅ SINGLE ITEM: Extraction result successfully sent for ${itemId}`);
      console.log(`🔄 SINGLE ITEM: UI should now update with new sales data`);

      // Close the tab after successful extraction
      setTimeout(() => {
        console.log(`🔒 SINGLE ITEM: Closing background tab after successful extraction`);
        window.close();
      }, 1000);
    }
  });
}

// 🔧 MANUAL DEBUG: Make extraction function globally available for console testing
window.debugExtractPurchaseHistory = function() {
  const urlParams = new URLSearchParams(window.location.search);
  const itemId = urlParams.get('item') || 'manual-test';
  console.log(`🔧 MANUAL DEBUG: Running extraction for ${itemId}`);
  extractPurchaseHistoryFromPage(itemId);
};

// Auto-execute if this script is injected
if (typeof window !== 'undefined') {
  console.log(`🔧 AUTHENTICATED DEBUG: Purchase history extractor loaded on: ${window.location.href}`);
  console.log(`🔧 AUTHENTICATED DEBUG: Page title: ${document.title}`);
  console.log(`🔧 AUTHENTICATED DEBUG: Page ready state: ${document.readyState}`);

  if (window.location.href.includes('purchaseHistory')) {
    // Extract item ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const itemId = urlParams.get('item');

    if (itemId) {
      console.log(`🚀 AUTHENTICATED DEBUG: Auto-executing extraction for item ${itemId}`);
      console.log(`🔧 AUTHENTICATED DEBUG: Waiting for page to fully load...`);

      // 🔍 DEBUG: Let's see what's actually happening in the background tab
      console.log(`🔍 DEBUG: Background tab loaded for ${itemId}`);
      console.log(`🔍 DEBUG: Current URL: ${window.location.href}`);
      console.log(`🔍 DEBUG: Page title: "${document.title}"`);
      console.log(`🔍 DEBUG: Page ready state: ${document.readyState}`);

      // 🚨 CRITICAL: Check for eBay authentication challenge
      if (window.location.href.includes('splashui/challenge')) {
        console.log(`🚨 AUTHENTICATION CHALLENGE: eBay is requesting authentication!`);
        console.log(`🚨 CHALLENGE URL: ${window.location.href}`);
        console.log(`🚨 SOLUTION: User needs to complete authentication in this tab`);
        sendExtractionResult(itemId, null);
        return;
      }

      // Check if we're actually on a purchase history page
      if (!window.location.href.includes('purchaseHistory')) {
        console.log(`❌ DEBUG: Not on purchase history page! URL: ${window.location.href}`);
        sendExtractionResult(itemId, null);
        return;
      }

      // Try extraction immediately
      console.log(`⚡ LIGHTNING FAST: Starting immediate extraction for ${itemId}`);
      extractPurchaseHistoryFromPage(itemId);

      // If that fails, try again after minimal delay
      setTimeout(() => {
        console.log(`⚡ RETRY: Second attempt for ${itemId}`);
        extractPurchaseHistoryFromPage(itemId);
      }, 2000); // 2 second delay for eBay to load

      // Final attempt after longer delay
      setTimeout(() => {
        console.log(`⚡ FINAL ATTEMPT: Third attempt for ${itemId}`);
        extractPurchaseHistoryFromPage(itemId);
      }, 5000); // 5 second delay

    } else {
      console.log(`⚠️ AUTHENTICATED DEBUG: No item ID found in URL: ${window.location.href}`);
      sendExtractionResult('unknown', null);
    }
  } else {
    console.log(`⚠️ AUTHENTICATED DEBUG: Not a purchase history page: ${window.location.href}`);
    sendExtractionResult('unknown', null);
  }
}
