/**
 * ✅ UNIVERSAL EBAY HANDLER - Handles basic messages on all eBay pages
 * Loads at document_start for immediate availability
 * Based on EbayLister4 reference pattern
 */

(function() {
  'use strict';
  
  console.log('🌐 Universal eBay Handler loading at document_start...');
  console.log('🚀 VERSION 2.0 - IMMEDIATE RESPONSE STRATEGY ACTIVE (2025-07-20)');
  
  /**
   * Handle basic messages that don't require complex dependencies
   */
  function handleMessage(message, sender, sendResponse) {
    console.log('📨 Universal eBay Handler received message:', message.action);
    
    try {
      // Handle ping requests
      if (message.action === 'ping') {
        console.log('🏓 Universal handler responding to ping');
        sendResponse({
          success: true,
          message: 'pong',
          handler: 'universal-ebay-handler',
          url: window.location.href,
          timestamp: Date.now()
        });
        return true;
      }
      
      // Handle content script readiness check
      if (message.action === 'checkContentScript') {
        console.log('✅ Universal handler confirming ready');
        sendResponse({
          success: true,
          ready: true,
          handler: 'universal-ebay-handler',
          url: window.location.href
        });
        return true;
      }
      
      // Handle startScraping directly as universal fallback
      if (message.action === 'startScraping') {
        console.log('🔄 Universal handler: Handling startScraping directly as fallback');
        console.log('🔍 Universal handler: Message details:', message);
        console.log('🔍 Universal handler: Current URL:', window.location.href);
        console.log('🔍 Universal handler: Page title:', document.title);
        console.log('🔍 Universal handler: Document ready state:', document.readyState);

        // ✅ CRITICAL FIX: Proper async handling with sendResponse in scope
        handleScrapingWithTimeout(message, sendResponse);
        return true; // Keep message channel open
      }

      // 🚀 NEW: Handle sales research requests
      if (message.action === 'startSalesResearch') {
        console.log('📈 Universal handler: Starting sales research for products');
        console.log('🔍 Products to research:', message.products?.length || 0);

        // Handle sales research with timeout protection
        handleSalesResearchWithTimeout(message, sendResponse);
        return true; // Keep message channel open for async response
      }

      // ✅ FIXED: Always handle startScraping in universal handler
      // The search handler delegation was causing the scraping to fail
      console.log('🔄 Universal handler: All messages handled by universal handler for reliability');
      


      // Handle testSelectors for debugging
      if (message.action === 'testSelectors') {
        console.log('🧪 Testing eBay selectors...');
        const testResults = testEbaySelectors();
        sendResponse({
          success: true,
          results: testResults,
          handler: 'universal-ebay-handler'
        });
        return true;
      }

      // Handle other basic messages
      console.log('❓ Universal handler - unhandled message:', message.action);
      sendResponse({
        success: false,
        error: 'Message not handled by universal handler',
        action: message.action,
        handler: 'universal-ebay-handler',
        suggestion: 'This message might need the search page handler'
      });
      
    } catch (error) {
      console.error('❌ Universal handler error:', error);
      sendResponse({
        success: false,
        error: 'Universal handler crashed: ' + error.message,
        action: message.action
      });
    }
    
    return true;
  }
  
  /**
   * ✅ CRITICAL FIX: Proper async scraping handler with timeout protection
   */
  async function handleScrapingWithTimeout(message, sendResponse) {
    console.log('🚀 Starting scraping with timeout protection...');

    // Set up timeout protection
    const timeoutId = setTimeout(() => {
      console.error('⏰ Scraping timeout after 12 seconds');
      sendResponse({
        success: false,
        error: 'Scraping timeout after 12 seconds',
        products: [],
        handler: 'universal-ebay-handler-timeout'
      });
    }, 30000); // 30 second timeout for basic scraping only

    try {
      // Quick selector test first - UPDATED TO CURRENT EBAY STRUCTURE
      console.log('🔍 Quick selector test with current eBay selectors...');

      // Try multiple selectors to catch all product containers
      const selectors = [
        'li[class*="s-item"]:not([class*="s-item--ad"])', // Exclude ads
        'li.s-card.s-card--horizontal',
        'li.s-card',
        'li[class*="s-card"]',
        'li[class*="s-item"]', // Alternative eBay structure
        '.s-item', // Direct class match
        '[data-testid*="item"]' // Test ID based
      ];

      let testElements = [];
      let usedSelector = '';

      for (const selector of selectors) {
        const elements = document.querySelectorAll(selector);
        console.log(`🔍 Testing "${selector}": ${elements.length} elements found`);
        if (elements.length >= testElements.length) {
          testElements = elements;
          usedSelector = selector;
        }
      }

      // Additional debug: check for any missed containers
      const allProductLinks = document.querySelectorAll('a[href*="/itm/"]');
      console.log(`🔍 Total product links found: ${allProductLinks.length}`);
      console.log(`🔍 Selected ${testElements.length} containers with "${usedSelector}"`);

      if (allProductLinks.length > testElements.length) {
        console.log(`⚠️ Potential missed products: ${allProductLinks.length - testElements.length}`);
      }

      console.log(`🔍 Using best selector "${usedSelector}" with ${testElements.length} elements`);

      if (testElements.length === 0) {
        clearTimeout(timeoutId);
        console.log('❌ No products found - page may not be loaded or selectors changed');
        sendResponse({
          success: true,
          products: [],
          error: 'No products found on page',
          handler: 'universal-ebay-handler',
          debug: {
            url: window.location.href,
            title: document.title,
            readyState: document.readyState,
            totalElements: document.querySelectorAll('*').length
          }
        });
        return;
      }

      // Extract products using streamlined logic
      console.log(`🚀 Extracting products from ${testElements.length} elements using selector "${usedSelector}"...`);
      const products = await worldClassExtraction(Array.from(testElements));

      console.log(`✅ Basic extraction completed: ${products.length} products`);

      // 🚀 CRITICAL: Send basic results immediately, enhance sales data in background
      // This prevents timeout issues while still providing sales enhancement
      clearTimeout(timeoutId);
      console.log(`🚀 IMMEDIATE RESPONSE STRATEGY: Sending ${products.length} products NOW (no waiting for sales data)`);
      console.log(`🚀 NEW FLOW ACTIVE: Background enhancement will run after response sent`);

      sendResponse({
        success: true,
        products: products,
        handler: 'universal-ebay-handler',
        action: message.action,
        totalFound: products.length,
        url: window.location.href,
        timestamp: Date.now(),
        basicScrapingComplete: true
      });

      return; // Exit early after sending response - NO automatic sales enhancement

    } catch (error) {
      clearTimeout(timeoutId);
      console.error('❌ Scraping failed:', error);
      sendResponse({
        success: false,
        error: 'Scraping failed: ' + error.message,
        products: [],
        handler: 'universal-ebay-handler',
        stack: error.stack
      });
    }
  }

  /**
   * 🚀 NEW: Sales research handler with timeout protection
   */
  async function handleSalesResearchWithTimeout(message, sendResponse) {
    console.log('📈 Starting sales research with timeout protection...');

    // Set up timeout protection
    const timeoutId = setTimeout(() => {
      console.error('⏰ Sales research timeout after 60 seconds');
      sendResponse({
        success: false,
        error: 'Sales research timeout after 60 seconds',
        handler: 'universal-ebay-handler-timeout'
      });
    }, 60000); // 60 second timeout for sales research

    try {
      const products = message.products || [];
      console.log(`📈 Starting sales research for ${products.length} products`);

      if (products.length === 0) {
        clearTimeout(timeoutId);
        sendResponse({
          success: false,
          error: 'No products provided for sales research',
          handler: 'universal-ebay-handler'
        });
        return;
      }

      // Send immediate response to confirm research started
      clearTimeout(timeoutId);
      sendResponse({
        success: true,
        message: 'Sales research started',
        productCount: products.length,
        handler: 'universal-ebay-handler'
      });

      // Start background sales enhancement
      console.log(`🚀 Starting sales data enhancement for ${products.length} products...`);
      enhanceWithSalesDataInBackground(products).then(() => {
        console.log('🚀 Sales research completed');
      }).catch(error => {
        console.warn('⚠️ Sales research failed:', error);
      });

    } catch (error) {
      clearTimeout(timeoutId);
      console.error('❌ Sales research failed:', error);
      sendResponse({
        success: false,
        error: 'Sales research failed: ' + error.message,
        handler: 'universal-ebay-handler',
        stack: error.stack
      });
    }
  }

  /**
   * 🚀 WORLD-CLASS EXTRACTION - Single unified extraction logic with comprehensive data
   */
  async function worldClassExtraction(listings) {
    console.log(`🚀 World-class extraction processing ${listings.length} listings...`);

    const products = [];
    const maxToProcess = Math.min(listings.length, 60); // Process all available products

    for (let i = 0; i < maxToProcess; i++) {
      try {
        const listing = listings[i];

        // Quick validation
        if (!listing || !listing.querySelector) {
          console.warn(`⚠️ Invalid listing element at index ${i}`);
          continue;
        }

        // 🚀 CURRENT EBAY SELECTORS - Based on actual li.s-card structure

        // Extract core data - FIXED: Based on actual eBay HTML structure

        // Updated structure for current eBay: <li class="s-item"><a href="/itm/"><div class="s-item__title">Title</div></a>
        const titleElement = listing.querySelector('.s-item__title, .s-card__title');
        const linkElement = listing.querySelector('a[href*="/itm/"]'); // The actual link element

        const title = titleElement ? titleElement.innerText.trim() : null;
        const url = linkElement ? linkElement.href : null;

        // DEBUG: Log first few extractions
        if (i < 3) {
          console.log(`🔍 Product ${i}: title="${title}" url="${url ? 'FOUND' : 'MISSING'}"`);
        }
        const price = extractCardPrice(listing);
        const shipping = extractCardShipping(listing);
        const condition = extractCardCondition(listing);
        const imageUrl = extractCardImage(listing);

        // Debug image extraction for first few products
        if (i < 3) {
          console.log(`🖼️ Image debug for product ${i + 1}:`, {
            imageUrl: imageUrl,
            hasImage: !!imageUrl,
            allImages: Array.from(listing.querySelectorAll('img')).map(img => ({
              src: img.src,
              className: img.className,
              alt: img.alt
            }))
          });
        }
        const soldDate = extractText(listing, '.s-card__ended-date');
        const seller = extractCardSeller(listing);
        const sellerFeedback = extractCardSellerFeedback(listing);
        const listingId = extractListingId(listing);
        const badges = extractCardBadges(listing);

        // 🚀 NEW: Sales frequency and history extraction
        const salesData = extractSalesData(listing);

        // Debug logging for first few products
        if (i < 3) {
          console.log(`🔍 Sales data for product ${i + 1}:`, salesData);
        }

        // Build data object with extracted values
        const data = {
          index: i, // 🔒 CRITICAL: Required for order preservation in popup - NEVER REMOVE
          extractedAt: new Date().toISOString(),

          // Core product data
          title: title,
          url: url,
          link: url, // UI expects 'link' field for clickable URLs
          price: price,
          shipping: shipping,
          condition: condition,
          imageUrl: imageUrl,
          soldDate: soldDate,

          // Current eBay seller info
          seller: seller,
          sellerFeedback: sellerFeedback,
          listingId: listingId,
          itemId: listingId, // UI expects 'itemId' field for display
          badges: badges,

          // Enhanced fields for rich UI display
          location: extractCardLocation(listing),
          bids: extractCardBids(listing),
          listingType: extractListingType(listing),
          bestOfferAccepted: price && typeof price === 'string' && price.includes('Best Offer'),
          freeReturns: badges.includes('Free returns'),
          category: extractText(listing, '.s-card__subtitle'),
          charity: badges.some(badge => badge.toLowerCase().includes('charity')),

          // 🚀 NEW: Sales frequency and history data
          salesFrequency: salesData.salesFrequency,
          salesHistory: salesData.salesHistory,
          lastSoldDate: salesData.lastSoldDate,
          averageSalePrice: salesData.averageSalePrice,
          salesVelocity: salesData.salesVelocity,
          demandScore: salesData.demandScore
        };

        // Skip promotional items
        if (data.title && data.title.includes('Shop on eBay')) {
          console.log(`⏭️ Skipping promotional item ${i}: "${data.title}"`);
          continue;
        }

        // Validate minimum required data
        if (!data.title || !data.url) {
          console.log(`⚠️ Skipping item ${i}: Missing required data (title: ${!!data.title}, url: ${!!data.url})`);
          continue;
        }

        // Calculate data quality
        data.extractionQuality = calculateQuality(data);
        data.source = 'universal-handler-world-class';

        // Debug logging for first few products to check data completeness
        if (i < 3) {
          console.log(`🔍 DEBUG Product ${i + 1} data:`, {
            title: !!data.title,
            price: !!data.price,
            url: !!data.url,
            itemId: !!data.itemId,
            condition: !!data.condition,
            seller: !!data.seller,
            imageUrl: !!data.imageUrl,
            imageUrlValue: data.imageUrl, // Show actual image URL
            shipping: !!data.shipping,
            sellerFeedback: !!data.sellerFeedback,
            badges: data.badges?.length || 0
          });
        }

        console.log(`📦 PRODUCT ${i + 1}: "${data.title}" - $${data.price} - ${data.condition} (${data.extractionQuality}% complete)`);
        products.push(data);

      } catch (error) {
        console.warn(`⚠️ Failed to extract product ${i}:`, error);
      }

      // Progress logging every 10 items
      if ((i + 1) % 10 === 0) {
        console.log(`📊 Progress: ${i + 1}/${maxToProcess} products processed, ${products.length} valid products found`);
      }
    }

    console.log(`✅ World-class extraction completed: ${products.length}/${listings.length} products extracted`);
    return products;
  }

  // Helper extraction functions
  function extractText(container, selector) {
    try {
      const element = container.querySelector(selector);
      return element ? element.textContent.trim() : null;
    } catch (error) {
      return null;
    }
  }

  function extractAttribute(container, selector, attribute) {
    try {
      const element = container.querySelector(selector);
      return element ? element.getAttribute(attribute) : null;
    } catch (error) {
      return null;
    }
  }

  // 🚀 CURRENT EBAY CARD EXTRACTION FUNCTIONS

  function extractCardPrice(container) {
    const priceElement = container.querySelector('.s-item__price, .s-card__price');
    if (!priceElement) return null;

    // Price can be complex (e.g., "$47.49Was: $49.99"). We only want the primary price.
    let price = priceElement.innerText.split(' ')[0].trim();

    // Handle cases where there was a best offer instead of a fixed price
    const bestOfferElement = container.querySelector('.s-item__price--accepted, .s-card__price--accepted');
    if (bestOfferElement) {
      price = bestOfferElement.innerText.trim();
    }

    // Extract numeric value
    const match = price.match(/[\d,]+\.?\d*/);
    return match ? parseFloat(match[0].replace(/,/g, '')) : null;
  }

  function extractCardShipping(container) {
    // 🔧 COMPREHENSIVE: Try multiple selector strategies to find shipping info
    const allSelectors = [
      'span.su-styled-text.secondary.large',  // Current primary selector
      '.s-item__shipping',                    // Legacy eBay selector
      '.s-card__shipping',                    // Card-based shipping
      '.s-card__delivery-cost',               // Delivery cost
      '.notranslate',                         // Sometimes shipping is in notranslate spans
      '[class*="shipping"]',                  // Any element with "shipping" in class
      '[class*="delivery"]',                  // Any element with "delivery" in class
      'span[class*="secondary"]'              // Secondary text spans
    ];

    let shippingElement = null;
    let foundText = '';

    // Try each selector strategy
    for (const selector of allSelectors) {
      const elements = container.querySelectorAll(selector);

      for (const element of elements) {
        const text = element.textContent.trim().toLowerCase();

        // Look for shipping/delivery indicators
        if (text.includes('delivery') || text.includes('shipping') ||
            text.includes('free') || text.match(/\$\d+\.?\d*/)) {
          shippingElement = element;
          foundText = element.textContent.trim();
          break;
        }
      }

      if (shippingElement) break;
    }

    // 🔧 DEBUG: Log what we found for first few products
    const productIndex = Array.from(container.parentElement?.children || []).indexOf(container) + 1;
    if (productIndex <= 3) {
      console.log(`🚢 SHIPPING DEBUG Product ${productIndex}:`, {
        found: !!shippingElement,
        text: foundText,
        allText: Array.from(container.querySelectorAll('*')).map(el => el.textContent.trim()).filter(t => t.includes('$') || t.includes('delivery') || t.includes('shipping') || t.includes('free')).slice(0, 5)
      });
    }

    if (!shippingElement) {
      // 🔧 CONSERVATIVE: Return null when we can't find shipping info
      // Let the UI handle the display logic
      console.log(`⚠️ No shipping element found for product ${productIndex}`);
      return null;
    }

    const shippingText = shippingElement.textContent.trim();

    if (shippingText.toLowerCase().includes('free')) {
      return 0;  // Return 0 for free shipping
    } else {
      // Extracts the price, e.g., "+$7.79 delivery" -> 7.79
      const match = shippingText.match(/\$(\d+\.?\d*)/);
      if (match) {
        const cost = parseFloat(match[1]);
        console.log(`💰 Found shipping cost: $${cost} from text: "${shippingText}"`);
        return cost;
      } else {
        console.log(`⚠️ No price found in shipping text: "${shippingText}"`);
        return null; // Return null if we can't parse the price
      }
    }
  }

  function extractCardCondition(container) {
    const conditionElement = container.querySelector('.s-item__subtitle, .s-card__subtitle');
    if (!conditionElement) return null;

    // The condition is often the first part of the subtitle text.
    return conditionElement.innerText.split('·')[0].trim();
  }

  function extractCardImage(container) {
    // Try multiple image selectors
    const imageSelectors = [
      'img.s-item__image',
      'img.s-card__image-img',
      'img[src*="ebayimg"]',
      'img[src*="i.ebayimg"]',
      '.s-item__image img',
      '.s-card__image img',
      'img'
    ];

    let imageElement = null;
    for (const selector of imageSelectors) {
      imageElement = container.querySelector(selector);
      if (imageElement && imageElement.src && !imageElement.src.includes('data:')) {
        break;
      }
    }

    if (!imageElement || !imageElement.src) return null;

    let imageUrl = imageElement.src;

    // eBay often uses low-res thumbnails in search. Replace for high-res version.
    if (imageUrl.includes('s-l225')) {
      imageUrl = imageUrl.replace('s-l225', 's-l1600');
    } else if (imageUrl.includes('s-l300')) {
      imageUrl = imageUrl.replace('s-l300', 's-l1600');
    } else if (imageUrl.includes('s-l140')) {
      imageUrl = imageUrl.replace('s-l140', 's-l1600');
    } else if (imageUrl.includes('s-l64')) {
      imageUrl = imageUrl.replace('s-l64', 's-l1600');
    }

    return imageUrl;
  }

  function extractCardSeller(container) {
    // Find seller spans using the regex pattern from your analysis
    const sellerSpans = Array.from(container.querySelectorAll('span')).filter(s => {
      const text = s.textContent.trim();
      return /^[a-zA-Z0-9.\-_]+$/.test(text) &&
             !/derosnopS|positive|\$|delivery|Pre-Owned|Brand New|Open Box|Buy It Now|or Best Offer|bids|United States/.test(text) &&
             text.length > 2; // Avoid very short matches
    });

    return sellerSpans.length > 0 ? sellerSpans[0].textContent.trim() : null;
  }

  function extractCardSellerFeedback(container) {
    // Find feedback spans with "% positive" pattern
    const feedbackSpans = Array.from(container.querySelectorAll('span')).filter(s =>
      /% positive/.test(s.textContent.trim())
    );

    return feedbackSpans.length > 0 ? feedbackSpans[0].textContent.trim() : null;
  }

  function extractListingId(container) {
    // Try multiple methods to extract eBay item ID

    // Method 1: Check data attributes
    if (container.dataset.listingid) {
      return container.dataset.listingid;
    }

    // Method 2: Extract from URL
    const linkElement = container.querySelector('a.su-link[href*="/itm/"]');
    if (linkElement && linkElement.href) {
      const urlMatch = linkElement.href.match(/\/itm\/([^\/\?]+)/);
      if (urlMatch) {
        return urlMatch[1];
      }
    }

    // Method 3: Check for any data-* attributes that might contain item ID
    for (const attr of container.attributes) {
      if (attr.name.includes('item') || attr.name.includes('listing')) {
        return attr.value;
      }
    }

    return null;
  }

  function extractCardBadges(container) {
    const badges = [];
    const badgeElements = container.querySelectorAll('.s-card__badge-icon');
    badgeElements.forEach(badge => {
      const badgeText = badge.innerText.trim();
      if (badgeText) {
        badges.push(badgeText);
      }
    });
    return badges;
  }

  function extractCardLocation(container) {
    // Look for location in the secondary large text elements
    const locationElements = container.querySelectorAll('span.su-styled-text.secondary.large');

    for (const element of locationElements) {
      const text = element.textContent.trim();
      if (text.toLowerCase().includes('located in') || text.toLowerCase().includes('from')) {
        return text.replace(/^(Located in|From)\s*/i, '');
      }
    }

    return null;
  }

  function extractCardBids(container) {
    // Look for bid information in secondary large text
    const bidElements = container.querySelectorAll('span.su-styled-text.secondary.large');

    for (const element of bidElements) {
      const text = element.textContent.trim();
      if (text.toLowerCase().includes('bid')) {
        return text;
      }
    }

    return null;
  }

  function extractListingType(container) {
    // Check for auction indicators
    const bidElements = container.querySelectorAll('span.su-styled-text.secondary.large');

    for (const element of bidElements) {
      const text = element.textContent.trim().toLowerCase();
      if (text.includes('bid')) {
        return 'Auction';
      }
    }

    // Check for Buy It Now indicators
    const priceElement = container.querySelector('.s-card__price');
    if (priceElement) {
      const priceText = priceElement.textContent.toLowerCase();
      if (priceText.includes('buy it now') || !priceText.includes('bid')) {
        return 'Buy It Now';
      }
    }

    return 'Buy It Now'; // Default assumption
  }

  // Legacy extraction functions for compatibility
  function extractPrice(container) {
    return extractCardPrice(container);
  }

  function extractShipping(container) {
    return extractCardShipping(container);
  }

  function extractImage(container) {
    return extractCardImage(container);
  }

  function extractBids(container) {
    const bidsText = extractText(container, 'span.s-item__bids');
    if (!bidsText) return null;

    const bidMatch = bidsText.match(/(\d+)\s*(bid|bids)/i);
    if (bidMatch) return { count: parseInt(bidMatch[1]), type: 'bids' };

    const watcherMatch = bidsText.match(/(\d+)\s*(watcher|watchers)/i);
    if (watcherMatch) return { count: parseInt(watcherMatch[1]), type: 'watchers' };

    return null;
  }

  function extractBoolean(container, selector) {
    try {
      return container.querySelector(selector) !== null;
    } catch (error) {
      return false;
    }
  }

  function extractFreeReturns(container) {
    const returnsElement = container.querySelector('.s-item__free-returns, .s-item__returns-accepted');
    if (!returnsElement) return false;

    const text = returnsElement.textContent?.toLowerCase() || '';
    return text.includes('free') || text.includes('return');
  }

  function calculateQuality(data) {
    // Core required fields (must have)
    const coreFields = ['title', 'price', 'url', 'itemId'];
    const coreScore = coreFields.filter(field => data[field] !== null && data[field] !== '').length / coreFields.length;

    // Enhanced fields (nice to have)
    const enhancedFields = ['condition', 'seller', 'imageUrl', 'shipping', 'sellerFeedback', 'badges', 'salesFrequency'];
    const enhancedScore = enhancedFields.filter(field => data[field] !== null && data[field] !== '').length / enhancedFields.length;

    // Weighted calculation: 70% core + 30% enhanced
    const totalScore = (coreScore * 0.7) + (enhancedScore * 0.3);
    return Math.round(totalScore * 100);
  }

  // 🚀 NEW: SALES FREQUENCY & HISTORY EXTRACTION FUNCTIONS

  /**
   * Extract sales data from listing element
   * Detects if on sold listings page and extracts sales history
   */
  function extractSalesData(container) {
    const isSoldListingsPage = window.location.href.includes('LH_Sold=1') ||
                               window.location.href.includes('LH_Complete=1') ||
                               document.title.toLowerCase().includes('sold');

    if (isSoldListingsPage) {
      return extractSoldListingData(container);
    } else {
      return extractActiveListingSalesData(container);
    }
  }

  /**
   * Extract sales data from sold listings page (FASTEST - direct extraction)
   */
  function extractSoldListingData(container) {
    try {
      // Extract sold date from sold listings
      const soldDate = extractSoldDate(container);
      const finalPrice = extractCardPrice(container);

      // Look for "X sold" text indicators (UPDATED WITH CORRECT SELECTORS)
      const soldCountText = extractText(container, '.s-item__caption, .s-item__sold, .NEGATIVE, .s-card__sold, .s-item__quantity-sold');
      const soldCount = extractSoldCount(soldCountText);

      return {
        salesFrequency: soldCount,
        salesHistory: soldDate ? [{ date: soldDate, price: finalPrice }] : [],
        lastSoldDate: soldDate,
        averageSalePrice: finalPrice,
        salesVelocity: calculateSalesVelocity(soldCount, soldDate),
        demandScore: calculateDemandScore(soldCount),
        isSoldListing: true,
        extractedFrom: 'sold-listings-page'
      };
    } catch (error) {
      console.warn('⚠️ Error extracting sold listing data:', error);
      return getEmptySalesData();
    }
  }

  /**
   * Extract sales data for active listings (requires background search)
   */
  function extractActiveListingSalesData(container) {
    // For active listings, we'll need to search sold listings
    // This will be enhanced in Phase 2 with background processing

    // Look for any existing sold indicators on active listings (UPDATED WITH CORRECT SELECTORS)
    const soldIndicators = container.querySelectorAll('.s-item__caption, .s-item__sold, .NEGATIVE, [class*="sold"]');
    let soldCount = 0;

    for (const indicator of soldIndicators) {
      const text = indicator.textContent.toLowerCase();
      const match = text.match(/(\d+)\s*sold/);
      if (match) {
        soldCount = parseInt(match[1]);
        break;
      }
    }

    return {
      salesFrequency: soldCount,
      salesHistory: [],
      lastSoldDate: null,
      averageSalePrice: null,
      salesVelocity: soldCount > 0 ? 'Unknown' : 0,
      demandScore: calculateDemandScore(soldCount),
      isSoldListing: false,
      extractedFrom: 'active-listing-indicators',
      needsBackgroundSearch: soldCount === 0 // Flag for background processing
    };
  }

  /**
   * Extract sold date from various eBay date formats
   */
  function extractSoldDate(container) {
    // Try multiple selectors for sold dates (UPDATED WITH CORRECT SELECTORS)
    const dateSelectors = [
      '.s-item__caption',        // 🚀 NEW: Primary sold date selector for 2025 eBay
      '.s-card__ended-date',
      '.s-item__ended-date',
      '.s-item__sold-date',
      '.NEGATIVE',
      '[class*="sold"]',
      '[class*="ended"]'
    ];

    for (const selector of dateSelectors) {
      const dateElement = container.querySelector(selector);
      if (dateElement) {
        const dateText = dateElement.textContent.trim();
        const parsedDate = parseSoldDate(dateText);
        if (parsedDate) return parsedDate;
      }
    }

    return null;
  }

  /**
   * Parse sold date from eBay text formats
   */
  function parseSoldDate(dateText) {
    if (!dateText) return null;

    try {
      // Common eBay sold date formats:
      // "Sold Mar 15, 2024"
      // "Sold 3d 2h"
      // "Ended Mar 15, 2024"

      // Handle relative dates (3d 2h, 1w 2d, etc.)
      const relativeMatch = dateText.match(/(\d+)([dwh])/g);
      if (relativeMatch) {
        let daysAgo = 0;
        for (const match of relativeMatch) {
          const [, num, unit] = match.match(/(\d+)([dwh])/);
          const value = parseInt(num);
          if (unit === 'd') daysAgo += value;
          else if (unit === 'w') daysAgo += value * 7;
          else if (unit === 'h') daysAgo += value / 24;
        }
        const soldDate = new Date();
        soldDate.setDate(soldDate.getDate() - daysAgo);
        return soldDate.toISOString().split('T')[0];
      }

      // Handle absolute dates (Mar 15, 2024)
      const absoluteMatch = dateText.match(/([A-Za-z]{3})\s+(\d{1,2}),?\s+(\d{4})/);
      if (absoluteMatch) {
        const [, month, day, year] = absoluteMatch;
        const date = new Date(`${month} ${day}, ${year}`);
        return date.toISOString().split('T')[0];
      }

      return null;
    } catch (error) {
      console.warn('⚠️ Error parsing sold date:', dateText, error);
      return null;
    }
  }

  /**
   * Extract sold count from text like "25 sold" or single sold items
   */
  function extractSoldCount(text) {
    if (!text) return 0;

    // Check for explicit count like "25 sold"
    const match = text.match(/(\d+)\s*sold/i);
    if (match) {
      return parseInt(match[1]);
    }

    // 🚀 NEW: For single sold items (like "Sold Jul 20, 2025"), count as 1
    if (text.match(/^Sold\s+[A-Za-z]{3}\s+\d{1,2},\s+\d{4}$/)) {
      return 1;
    }

    return 0;
  }

  /**
   * Calculate sales velocity (sales per month)
   */
  function calculateSalesVelocity(soldCount, lastSoldDate) {
    if (!soldCount || soldCount === 0) return 0;
    if (!lastSoldDate) return 'Unknown';

    try {
      const soldDate = new Date(lastSoldDate);
      const now = new Date();
      const daysDiff = (now - soldDate) / (1000 * 60 * 60 * 24);

      if (daysDiff <= 0) return soldCount; // Sold today

      const monthsDiff = daysDiff / 30;
      return Math.round((soldCount / monthsDiff) * 10) / 10; // Round to 1 decimal
    } catch (error) {
      return 'Unknown';
    }
  }

  /**
   * Calculate demand score based on sales frequency
   */
  function calculateDemandScore(soldCount) {
    if (!soldCount || soldCount === 0) return 0;

    // Scoring system:
    // 1-5 sold = Low (1-2)
    // 6-15 sold = Medium (3-5)
    // 16-50 sold = High (6-8)
    // 50+ sold = Very High (9-10)

    if (soldCount <= 5) return Math.min(2, soldCount);
    if (soldCount <= 15) return Math.min(5, 2 + Math.floor(soldCount / 3));
    if (soldCount <= 50) return Math.min(8, 5 + Math.floor(soldCount / 10));
    return Math.min(10, 8 + Math.floor(soldCount / 25));
  }

  /**
   * Return empty sales data structure
   */
  function getEmptySalesData() {
    return {
      salesFrequency: 0,
      salesHistory: [],
      lastSoldDate: null,
      averageSalePrice: null,
      salesVelocity: 0,
      demandScore: 0,
      isSoldListing: false,
      extractedFrom: 'none',
      needsBackgroundSearch: true
    };
  }

  // 🚀 NEW: Fast background sales data enhancement (non-blocking)
  async function enhanceWithSalesDataInBackground(products) {
    // Process ALL products with valid item IDs
    const productsToEnhance = products.filter(product =>
      product.itemId &&
      product.itemId !== 'unknown' &&
      product.itemId.length > 5
    ); // Process ALL products, not just first 20

    if (productsToEnhance.length === 0) {
      console.log('📊 No products selected for background sales enhancement');
      return;
    }

    console.log(`🚀 Starting BACKGROUND sales enhancement for ${productsToEnhance.length} products (limited for performance)`);

    // Check if SalesHistoryFetcher is available
    if (typeof window.SalesHistoryFetcher === 'undefined') {
      console.error('❌ CRITICAL: SalesHistoryFetcher not available for background enhancement');
      console.log('🔧 CRITICAL: Available window objects:', Object.keys(window).filter(k => k.includes('Sales') || k.includes('History')));
      return;
    }

    console.log('✅ CRITICAL: SalesHistoryFetcher is available, creating instance...');

    try {
      const salesFetcher = new window.SalesHistoryFetcher();
      console.log('✅ CRITICAL: SalesHistoryFetcher instance created successfully');

      // 🚀 ENTERPRISE: Process ALL products with intelligent batching (NO TIMEOUT)
      const batchSize = 10; // Larger batches for efficiency
      let processedCount = 0;
      let successCount = 0;

      console.log(`⚡ LIGHTNING FAST: Processing ALL ${productsToEnhance.length} products with rapid background tabs`);
      const startTime = Date.now();

      for (let i = 0; i < productsToEnhance.length; i += batchSize) {
        const batch = productsToEnhance.slice(i, i + batchSize);
        const batchNum = Math.floor(i/batchSize) + 1;
        const totalBatches = Math.ceil(productsToEnhance.length / batchSize);

        console.log(`⚡ LIGHTNING BATCH ${batchNum}/${totalBatches}: Processing ${batch.length} products (${i + 1}-${Math.min(i + batchSize, productsToEnhance.length)} of ${productsToEnhance.length})`);

        // Show which items we're processing
        batch.forEach((product, idx) => {
          console.log(`⚡ Item ${i + idx + 1}: ${product.itemId} - ${product.title?.substring(0, 40)}...`);
        });

        // Process batch with timeout protection
        const promises = batch.map(async (product) => {
          try {
            // 🚀 SIMPLIFIED: For sold listings, we know they sold at least once
            // Try to get purchase history, but fallback to basic sold listing data
            let salesData;

            try {
              console.log(`🎯 CRITICAL: Attempting purchase history for ${product.itemId}: ${product.title?.substring(0, 30)}...`);

              // 🚀 AUTHENTICATED: Let background tab system handle its own timeout (30 seconds)
              salesData = await salesFetcher.fetchSalesHistory(product);

              console.log(`✅ CRITICAL: Purchase history fetch SUCCESS for ${product.itemId}:`, salesData);
            } catch (error) {
              console.error(`❌ CRITICAL: Sales history fetch failed for ${product.itemId} (${product.title?.substring(0, 30)}...):`, error);
              console.log(`🔧 CRITICAL: Falling back to sold listing data for ${product.itemId}`);
              salesData = null;
            }

            // Always update with at least basic sales information
            if (salesData && salesData.salesFrequency > 0) {
              // Use fetched sales data
              Object.assign(product, {
                salesFrequency: salesData.salesFrequency,
                salesHistory: salesData.salesHistory || [],
                lastSoldDate: salesData.lastSoldDate,
                averageSalePrice: salesData.averageSalePrice,
                salesVelocity: salesData.salesVelocity || 0,
                demandScore: salesData.demandScore || 0,
                extractedFrom: salesData.extractedFrom,
                purchaseHistoryChecked: true
              });
              console.log(`✅ Enhanced ${product.title?.substring(0, 30)}... with ${salesData.salesFrequency} sales from ${salesData.extractedFrom}`);
            } else {
              // 🔧 CRITICAL: Enhanced fallback with better date handling
              console.log(`🔧 CRITICAL: Using enhanced fallback for ${product.itemId} with soldDate: ${product.soldDate}`);

              // Create a more realistic sales history entry
              const fallbackHistory = [];
              if (product.soldDate) {
                // Use the actual sold date if available
                fallbackHistory.push({
                  date: product.soldDate,
                  formattedDate: product.soldDate, // Will be formatted by UI
                  price: product.price,
                  quantity: 1,
                  source: 'sold-listing-fallback'
                });
              }

              Object.assign(product, {
                salesFrequency: 1,
                salesHistory: fallbackHistory,
                lastSoldDate: product.soldDate || null,
                averageSalePrice: product.price || null,
                salesVelocity: 1, // 1 sale in unknown timeframe
                demandScore: 1, // Low demand (only 1 sale)
                extractedFrom: 'sold-listing-fallback', // Changed to indicate this is fallback
                purchaseHistoryChecked: true,
                needsPurchaseHistoryRetry: true // Flag for potential retry
              });
              console.log(`📊 CRITICAL: Set enhanced fallback data for ${product.title?.substring(0, 30)}... (soldDate: ${product.soldDate})`);
            }
          } catch (error) {
            console.log(`⚠️ Background enhancement failed for ${product.title?.substring(0, 30)}...`);
            // Still mark as checked with minimal data
            Object.assign(product, {
              salesFrequency: 0,
              salesHistory: [],
              lastSoldDate: null,
              averageSalePrice: null,
              salesVelocity: 0,
              demandScore: 0,
              extractedFrom: 'enhancement-failed',
              purchaseHistoryChecked: true
            });
          }
        });

        await Promise.allSettled(promises);

        // ⚡ LIGHTNING PROGRESS: Track rapid progress
        processedCount += batch.length;
        const progressPercent = Math.round((processedCount / productsToEnhance.length) * 100);
        const elapsed = ((Date.now() - startTime) / 1000).toFixed(1);
        const rate = (processedCount / elapsed).toFixed(1);

        console.log(`⚡ LIGHTNING PROGRESS: ${processedCount}/${productsToEnhance.length} (${progressPercent}%) - ${elapsed}s elapsed - ${rate} items/sec`);

        // Minimal delay between batches for maximum speed
        if (i + batchSize < productsToEnhance.length) {
          await new Promise(resolve => setTimeout(resolve, 100)); // Ultra-fast 0.1s delay
        }
      }

      const enhancedProducts = products.filter(p => p.purchaseHistoryChecked);
      const finalSuccessRate = Math.round((successCount / productsToEnhance.length) * 100);
      const totalTime = ((Date.now() - startTime) / 1000).toFixed(1);
      const finalRate = (productsToEnhance.length / totalTime).toFixed(1);

      console.log(`⚡ LIGHTNING FAST: Sales enhancement COMPLETED in ${totalTime} seconds!`);
      console.log(`⚡ LIGHTNING RESULTS: ${successCount}/${productsToEnhance.length} successful (${finalSuccessRate}% success rate)`);
      console.log(`⚡ LIGHTNING SPEED: Processed ${finalRate} items/second - BLAZING FAST!`);

      // 🚀 NEW: Notify service worker that sales data has been updated
      try {
        console.log(`📡 Sending sales data update: ${enhancedProducts.length} products with sales data`);

        chrome.runtime.sendMessage({
          action: 'salesDataUpdated',
          products: products,
          enhancedCount: enhancedProducts.length,
          totalProducts: products.length,
          timestamp: Date.now()
        });
        console.log(`📡 Successfully notified service worker of sales data updates`);
      } catch (error) {
        console.log(`⚠️ Could not notify service worker of sales updates:`, error);
      }

    } catch (error) {
      console.warn('⚠️ Error during background sales enhancement:', error);
    }
  }

  // 🚀 LEGACY: Synchronous sales data enhancement (now unused due to timeout issues)
  async function enhanceWithSalesDataSynchronously(products) {
    // Enhance ALL products with valid item IDs using direct purchase history endpoint
    const productsNeedingEnhancement = products.filter(product =>
      product.itemId &&
      product.itemId !== 'unknown' &&
      product.itemId.length > 5 // Valid eBay item IDs are longer than 5 characters
    );

    if (productsNeedingEnhancement.length === 0) {
      console.log('📊 No products with valid item IDs for sales enhancement');
      return;
    }

    console.log(`🚀 Starting SYNCHRONOUS sales enhancement for ${productsNeedingEnhancement.length} products`);

    // Check if SalesHistoryFetcher is available
    if (typeof window.SalesHistoryFetcher === 'undefined') {
      console.warn('⚠️ SalesHistoryFetcher not available for sales enhancement');
      return;
    }

    try {
      const salesFetcher = new window.SalesHistoryFetcher();

      // Process products in smaller batches with progress logging
      const batchSize = 10; // Larger batches for faster processing
      let completed = 0;

      // 🚀 ENTERPRISE: Process ALL products (NO TIMEOUT LIMITS)
      console.log(`🎯 ENTERPRISE: Processing ALL ${productsNeedingEnhancement.length} products synchronously`);

      for (let i = 0; i < productsNeedingEnhancement.length; i += batchSize) {
        // 🚀 NO TIMEOUT CHECK - Process everything!

        const batch = productsNeedingEnhancement.slice(i, i + batchSize);

        console.log(`🔍 Processing sales data batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(productsNeedingEnhancement.length/batchSize)} (${batch.length} products)`);

        // Process batch in parallel
        const promises = batch.map(async (product) => {
          try {
            console.log(`🎯 Fetching purchase history for item ${product.itemId}: ${product.title?.substring(0, 40)}...`);

            const salesData = await salesFetcher.fetchSalesHistory(product);

            if (salesData) {
              // Always update with the fetched data, even if salesFrequency is 0
              Object.assign(product, {
                salesFrequency: salesData.salesFrequency || 0,
                salesHistory: salesData.salesHistory || [],
                lastSoldDate: salesData.lastSoldDate,
                averageSalePrice: salesData.averageSalePrice,
                salesVelocity: salesData.salesVelocity || 0,
                demandScore: salesData.demandScore || 0,
                extractedFrom: salesData.extractedFrom || 'direct-purchase-history-attempted',
                needsBackgroundSearch: false,
                purchaseHistoryChecked: true,
                totalTransactions: salesData.totalTransactions || 0
              });

              if (salesData.salesFrequency > 0) {
                console.log(`✅ Enhanced ${product.title?.substring(0, 30)}... with ${salesData.salesFrequency} sales from ${salesData.extractedFrom}`);
              } else {
                console.log(`📭 No sales found for ${product.title?.substring(0, 30)}... via ${salesData.extractedFrom}`);
              }
            } else {
              // Mark as checked even if no data returned
              product.purchaseHistoryChecked = true;
              product.extractedFrom = 'purchase-history-unavailable';
              console.log(`⚠️ No purchase history data available for ${product.title?.substring(0, 30)}...`);
            }
          } catch (error) {
            console.warn(`❌ Failed to enhance product ${product.title?.substring(0, 30)}...`, error);
            // Mark as checked to avoid retry loops
            product.purchaseHistoryChecked = true;
            product.extractedFrom = 'purchase-history-error';
          }
        });

        await Promise.allSettled(promises);
        completed += batch.length;

        console.log(`📊 Sales enhancement progress: ${completed}/${productsNeedingEnhancement.length} products completed`);

        // Send progress update to service worker
        const progressPercent = 40 + Math.floor((completed / productsNeedingEnhancement.length) * 50); // 40-90% range
        try {
          chrome.runtime.sendMessage({
            action: 'updateProgress',
            progress: progressPercent,
            status: `Enhanced ${completed}/${productsNeedingEnhancement.length} products with sales data...`
          });
        } catch (e) {
          console.log('📡 Could not send progress update to service worker');
        }

        // Small delay between batches to be respectful to eBay's servers
        if (i + batchSize < productsNeedingEnhancement.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log(`✅ SYNCHRONOUS sales enhancement completed for ${productsNeedingEnhancement.length} products`);

    } catch (error) {
      console.warn('⚠️ Error during synchronous sales enhancement:', error);
    }
  }

  // 🚀 LEGACY: Keep background enhancement for compatibility (now unused)
  async function enhanceWithBackgroundSalesData(products) {
    // Enhance ALL products with valid item IDs using direct purchase history endpoint
    const productsNeedingEnhancement = products.filter(product =>
      product.itemId &&
      product.itemId !== 'unknown' &&
      product.itemId.length > 5 // Valid eBay item IDs are longer than 5 characters
    );

    if (productsNeedingEnhancement.length === 0) {
      console.log('📊 No products with valid item IDs for sales enhancement');
      return;
    }

    console.log(`🚀 Starting AGGRESSIVE sales enhancement for ${productsNeedingEnhancement.length} products using direct purchase history endpoint`);

    // Check if SalesHistoryFetcher is available
    if (typeof window.SalesHistoryFetcher === 'undefined') {
      console.warn('⚠️ SalesHistoryFetcher not available for background enhancement');
      return;
    }

    try {
      const salesFetcher = new window.SalesHistoryFetcher();

      // Process products in small batches to avoid overwhelming the system
      const batchSize = 3;
      for (let i = 0; i < productsNeedingEnhancement.length; i += batchSize) {
        const batch = productsNeedingEnhancement.slice(i, i + batchSize);

        // Process batch in parallel
        const promises = batch.map(async (product) => {
          try {
            console.log(`🎯 Fetching purchase history for item ${product.itemId}: ${product.title?.substring(0, 40)}...`);

            const salesData = await salesFetcher.fetchSalesHistory(product);

            if (salesData) {
              // Always update with the fetched data, even if salesFrequency is 0
              Object.assign(product, {
                salesFrequency: salesData.salesFrequency || 0,
                salesHistory: salesData.salesHistory || [],
                lastSoldDate: salesData.lastSoldDate,
                averageSalePrice: salesData.averageSalePrice,
                salesVelocity: salesData.salesVelocity || 0,
                demandScore: salesData.demandScore || 0,
                extractedFrom: salesData.extractedFrom || 'direct-purchase-history-attempted',
                needsBackgroundSearch: false,
                purchaseHistoryChecked: true,
                totalTransactions: salesData.totalTransactions || 0
              });

              if (salesData.salesFrequency > 0) {
                console.log(`✅ Enhanced ${product.title?.substring(0, 30)}... with ${salesData.salesFrequency} sales from ${salesData.extractedFrom}`);
              } else {
                console.log(`📭 No sales found for ${product.title?.substring(0, 30)}... via ${salesData.extractedFrom}`);
              }
            } else {
              // Mark as checked even if no data returned
              product.purchaseHistoryChecked = true;
              product.extractedFrom = 'purchase-history-unavailable';
              console.log(`⚠️ No purchase history data available for ${product.title?.substring(0, 30)}...`);
            }
          } catch (error) {
            console.warn(`❌ Failed to enhance product ${product.title?.substring(0, 30)}...`, error);
            // Mark as checked to avoid retry loops
            product.purchaseHistoryChecked = true;
            product.extractedFrom = 'purchase-history-error';
          }
        });

        await Promise.allSettled(promises);

        // Small delay between batches
        if (i + batchSize < productsNeedingEnhancement.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      console.log(`✅ Background sales enhancement completed for ${productsNeedingEnhancement.length} products`);

    } catch (error) {
      console.warn('⚠️ Error during background sales enhancement:', error);
    }
  }





  /**
   * Extract sales data for active listings (requires background search)
   */
  function extractActiveListingSalesData(container) {
    // For active listings, we'll need to search sold listings
    // This will be enhanced in Phase 2 with background processing

    // Look for any existing sold indicators on active listings
    const soldIndicators = container.querySelectorAll('.s-item__sold, .NEGATIVE, [class*="sold"]');
    let soldCount = 0;

    for (const indicator of soldIndicators) {
      const text = indicator.textContent.toLowerCase();
      const match = text.match(/(\d+)\s*sold/);
      if (match) {
        soldCount = parseInt(match[1]);
        break;
      }
    }

    return {
      salesFrequency: soldCount,
      salesHistory: [],
      lastSoldDate: null,
      averageSalePrice: null,
      salesVelocity: soldCount > 0 ? 'Unknown' : 0,
      demandScore: calculateDemandScore(soldCount),
      isSoldListing: false,
      extractedFrom: 'active-listing-indicators',
      needsBackgroundSearch: soldCount === 0 // Flag for background processing
    };
  }

  /**
   * Test eBay selectors for debugging
   */
  function testEbaySelectors() {
    console.log('🧪 Testing CURRENT eBay selectors...');

    // Test multiple container selectors to find the best one
    const containerSelectors = [
      'li[class*="s-item"]:not([class*="s-item--ad"])',
      'li.s-card.s-card--horizontal',
      'li.s-card',
      'li[class*="s-card"]',
      'li[class*="s-item"]'
    ];

    let bestContainerSelector = containerSelectors[0];
    let maxContainers = 0;

    containerSelectors.forEach(selector => {
      const count = document.querySelectorAll(selector).length;
      console.log(`🔍 Container test "${selector}": ${count} elements`);
      if (count > maxContainers) {
        maxContainers = count;
        bestContainerSelector = selector;
      }
    });

    const selectors = {
      containers: bestContainerSelector,
      title: '.s-item__title, .s-card__title',
      link: 'a[href*="/itm/"]', // The actual link element
      price: '.s-item__price, .s-card__price',
      condition: '.s-item__subtitle, .s-card__subtitle',
      seller: '.s-item__seller-info-text, .s-card__seller-info-text',
      image: 'img.s-item__image, img.s-card__image-img',
      shipping: '.s-item__shipping, .su-styled-text.secondary.large, .s-card__shipping, .s-card__delivery-cost',
      badges: '.s-item__badge-icon, .s-card__badge-icon'
    };

    const results = {};

    Object.entries(selectors).forEach(([name, selector]) => {
      const elements = document.querySelectorAll(selector);
      results[name] = {
        selector: selector,
        found: elements.length,
        sample: elements.length > 0 ? elements[0].textContent?.trim().substring(0, 100) : null
      };
      console.log(`🔍 ${name}: ${elements.length} elements found`);
    });

    return results;
  }

  /**
   * Check if current page is a search page
   */
  function isSearchPage() {
    const url = window.location.href;
    return url.includes('/sch/i.html') && url.includes('_nkw=');
  }
  
  /**
   * Check if message is search-specific
   */
  function isSearchSpecificMessage(action) {
    const searchActions = [
      'startScraping',
      'testSelectors', 
      'minimalTest',
      'scanCurrentPage'
    ];
    return searchActions.includes(action);
  }
  
  // ✅ CRITICAL: Register message listener immediately
  chrome.runtime.onMessage.addListener(handleMessage);
  
  // Send ready notification to service worker
  function notifyServiceWorker() {
    chrome.runtime.sendMessage({
      action: 'contentScriptReady',
      handler: 'universal-ebay-handler',
      url: window.location.href,
      timestamp: Date.now(),
      isSearchPage: isSearchPage()
    }).catch(error => {
      console.log('📡 Service worker not ready yet for universal handler');
    });
  }
  
  // Notify service worker when ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', notifyServiceWorker);
  } else {
    notifyServiceWorker();
  }
  
  // Global status for debugging
  window.universalEbayHandler = {
    isReady: () => true,
    getUrl: () => window.location.href,
    isSearchPage: isSearchPage
  };
  
  console.log('✅ Universal eBay Handler setup complete');
  
})();
